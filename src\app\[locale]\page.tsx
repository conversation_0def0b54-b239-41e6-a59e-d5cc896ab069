'use client';

import { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import LanguageToggle from '@/components/LanguageToggle';
import FileUpload from '@/components/FileUpload';
import ProgressIndicator from '@/components/ProgressIndicator';
import DownloadSection from '@/components/DownloadSection';
import { useVideoConverter } from '@/hooks/useVideoConverter';

export default function Home() {
  const t = useTranslations();
  const tErrors = useTranslations('errors');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [downloadUrl, setDownloadUrl] = useState<string>('');

  const {
    convertVideo,
    state: conversionState,
    progress,
    stage: progressStage,
    error: conversionError,
    reset: resetConverter
  } = useVideoConverter();

  const handleFileSelect = async (file: File) => {
    setSelectedFile(file);
    resetConverter();

    // Start conversion automatically
    try {
      const url = await convertVideo(file);
      if (url) {
        setDownloadUrl(url);
      }
    } catch (err) {
      console.error('Conversion failed:', err);
    }
  };

  const handleClearFile = () => {
    setSelectedFile(null);
    resetConverter();
    if (downloadUrl) {
      URL.revokeObjectURL(downloadUrl);
      setDownloadUrl('');
    }
  };

  const handleConvertAnother = () => {
    handleClearFile();
  };

  // Clean up download URL on unmount
  useEffect(() => {
    return () => {
      if (downloadUrl) {
        URL.revokeObjectURL(downloadUrl);
      }
    };
  }, [downloadUrl]);

  const isProcessing = conversionState === 'loading' || conversionState === 'converting';
  const error = conversionError;

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="w-full p-4 sm:p-6">
        <div className="max-w-6xl mx-auto flex justify-between items-center">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-800">
              {t('title')}
            </h1>
            <p className="text-gray-600 mt-1 text-sm sm:text-base">
              {t('subtitle')}
            </p>
          </div>
          <LanguageToggle />
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 py-8">
        {/* Instructions */}
        <div className="text-center mb-12">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-3xl mx-auto">
            <div className="bg-white p-6 rounded-lg shadow-sm border">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-blue-600 font-bold text-lg">1</span>
              </div>
              <h3 className="font-semibold text-gray-800 mb-2">
                {t('steps.step1')}
              </h3>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-sm border">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-blue-600 font-bold text-lg">2</span>
              </div>
              <h3 className="font-semibold text-gray-800 mb-2">
                {t('steps.step2')}
              </h3>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-sm border">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-blue-600 font-bold text-lg">3</span>
              </div>
              <h3 className="font-semibold text-gray-800 mb-2">
                {t('steps.step3')}
              </h3>
            </div>
          </div>
        </div>

        {/* Conversion Interface */}
        <div className="flex justify-center">
          {conversionState === 'completed' && downloadUrl ? (
            <DownloadSection
              downloadUrl={downloadUrl}
              originalFileName={selectedFile?.name || 'video'}
              onConvertAnother={handleConvertAnother}
            />
          ) : isProcessing ? (
            <ProgressIndicator
              progress={progress}
              stage={progressStage}
            />
          ) : (
            <FileUpload
              onFileSelect={handleFileSelect}
              isProcessing={isProcessing}
              selectedFile={selectedFile}
              onClearFile={handleClearFile}
            />
          )}
        </div>

        {/* Error Display */}
        {error && (
          <div className="mt-6 max-w-md mx-auto p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-700 text-center">{error}</p>
          </div>
        )}
      </main>
    </div>
  );
}
