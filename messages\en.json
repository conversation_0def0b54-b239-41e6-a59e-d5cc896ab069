{"title": "<PERSON>M Converter", "subtitle": "Convert MP4 videos to WebM format with 512x512 resizing", "steps": {"step1": "Step 1: Upload your MP4 file", "step2": "Step 2: Wait for automatic conversion", "step3": "Step 3: Download your new WebM file"}, "upload": {"dragDrop": "Drag and drop your MP4 file here", "or": "or", "clickToSelect": "Click to select file", "maxSize": "Max file size: 100MB", "supportedFormats": "Supported format: MP4", "uploading": "Uploading...", "processing": "Converting video...", "success": "Conversion complete!", "downloadReady": "Your WebM file is ready for download"}, "buttons": {"selectFile": "Select File", "download": "Download WebM", "convertAnother": "Convert Another File", "cancel": "Cancel"}, "errors": {"unsupportedFormat": "Unsupported file format. Please select an MP4 file.", "fileTooLarge": "File is too large. Maximum size is 100MB.", "conversionFailed": "Conversion failed. Please try again.", "uploadFailed": "Upload failed. Please try again.", "noFileSelected": "No file selected. Please choose an MP4 file."}, "language": {"switch": "Switch to French", "current": "English"}, "progress": {"uploading": "Uploading: {progress}%", "converting": "Converting: {progress}%", "preparing": "Preparing conversion...", "finalizing": "Finalizing..."}}