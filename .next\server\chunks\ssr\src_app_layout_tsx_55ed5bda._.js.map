{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Billie/src/app/layout.tsx"], "sourcesContent": ["import { ReactNode } from 'react';\n\ntype Props = {\n  children: ReactNode;\n};\n\n// Since we have a `not-found.tsx` page on the root, a layout file\n// is required, even if it's just passing children through.\nexport default function RootLayout({ children }: Props) {\n  return children;\n}\n"], "names": [], "mappings": ";;;AAQe,SAAS,WAAW,EAAE,QAAQ,EAAS;IACpD,OAAO;AACT", "debugId": null}}]}