'use client';

import { useTranslations } from 'next-intl';
import { Download, RotateCcw, CheckCircle } from 'lucide-react';

interface DownloadSectionProps {
  downloadUrl: string;
  originalFileName: string;
  onConvertAnother: () => void;
}

export default function DownloadSection({ 
  downloadUrl, 
  originalFileName, 
  onConvertAnother 
}: DownloadSectionProps) {
  const t = useTranslations('upload');
  const tButtons = useTranslations('buttons');

  const handleDownload = () => {
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = originalFileName.replace(/\.[^/.]+$/, '.webm');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div className="w-full max-w-md mx-auto p-6 bg-white rounded-lg border-2 border-green-200 shadow-lg">
      <div className="text-center mb-6">
        <CheckCircle className="mx-auto text-green-600 mb-3" size={48} />
        <h3 className="text-xl font-semibold text-gray-800 mb-2">
          {t('success')}
        </h3>
        <p className="text-gray-600">
          {t('downloadReady')}
        </p>
      </div>
      
      <div className="space-y-3">
        <button
          onClick={handleDownload}
          className="w-full flex items-center justify-center gap-3 px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors duration-200 font-medium"
        >
          <Download size={20} />
          {tButtons('download')}
        </button>
        
        <button
          onClick={onConvertAnother}
          className="w-full flex items-center justify-center gap-3 px-6 py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-colors duration-200 font-medium"
        >
          <RotateCcw size={20} />
          {tButtons('convertAnother')}
        </button>
      </div>
    </div>
  );
}
