'use client';

import { useState, useCallback } from 'react';
import { FFmpeg } from '@ffmpeg/ffmpeg';
import { fetchFile, toBlobURL } from '@ffmpeg/util';

type ConversionState = 'idle' | 'loading' | 'converting' | 'completed' | 'error';
type ProgressStage = 'uploading' | 'converting' | 'preparing' | 'finalizing';

interface UseVideoConverterReturn {
  convertVideo: (file: File) => Promise<string | null>;
  state: ConversionState;
  progress: number;
  stage: ProgressStage;
  error: string | null;
  reset: () => void;
}

export function useVideoConverter(): UseVideoConverterReturn {
  const [state, setState] = useState<ConversionState>('idle');
  const [progress, setProgress] = useState(0);
  const [stage, setStage] = useState<ProgressStage>('preparing');
  const [error, setError] = useState<string | null>(null);
  const [ffmpeg] = useState(() => new FFmpeg());

  const reset = useCallback(() => {
    setState('idle');
    setProgress(0);
    setStage('preparing');
    setError(null);
  }, []);

  const loadFFmpeg = useCallback(async () => {
    if (ffmpeg.loaded) return;

    try {
      setStage('preparing');
      setProgress(10);

      // Load FFmpeg with CDN URLs
      const baseURL = 'https://unpkg.com/@ffmpeg/core@0.12.6/dist/esm';
      
      await ffmpeg.load({
        coreURL: await toBlobURL(`${baseURL}/ffmpeg-core.js`, 'text/javascript'),
        wasmURL: await toBlobURL(`${baseURL}/ffmpeg-core.wasm`, 'application/wasm'),
      });

      setProgress(30);
    } catch (err) {
      console.error('Failed to load FFmpeg:', err);
      throw new Error('Failed to initialize video converter');
    }
  }, [ffmpeg]);

  const convertVideo = useCallback(async (file: File): Promise<string | null> => {
    try {
      setState('loading');
      setError(null);
      setProgress(0);

      // Load FFmpeg
      await loadFFmpeg();
      setProgress(40);

      setState('converting');
      setStage('converting');

      // Write input file
      await ffmpeg.writeFile('input.mp4', await fetchFile(file));
      setProgress(50);

      // Set up progress tracking
      ffmpeg.on('progress', ({ progress: ffmpegProgress }) => {
        const adjustedProgress = 50 + (ffmpegProgress * 40); // 50-90%
        setProgress(adjustedProgress);
      });

      setStage('converting');

      // Convert video with scaling to fit 512x512 while preserving aspect ratio
      // The scale filter will scale the video to fit within 512x512 and pad with black bars if needed
      await ffmpeg.exec([
        '-i', 'input.mp4',
        '-vf', 'scale=512:512:force_original_aspect_ratio=decrease,pad=512:512:(ow-iw)/2:(oh-ih)/2:black',
        '-c:v', 'libvpx-vp9',
        '-crf', '30',
        '-b:v', '0',
        '-b:a', '128k',
        '-c:a', 'libopus',
        '-f', 'webm',
        'output.webm'
      ]);

      setStage('finalizing');
      setProgress(95);

      // Read output file
      const data = await ffmpeg.readFile('output.webm');
      const blob = new Blob([data], { type: 'video/webm' });
      const url = URL.createObjectURL(blob);

      // Clean up
      await ffmpeg.deleteFile('input.mp4');
      await ffmpeg.deleteFile('output.webm');

      setProgress(100);
      setState('completed');

      return url;
    } catch (err) {
      console.error('Conversion error:', err);
      setError(err instanceof Error ? err.message : 'Conversion failed');
      setState('error');
      return null;
    }
  }, [ffmpeg, loadFFmpeg]);

  return {
    convertVideo,
    state,
    progress,
    stage,
    error,
    reset,
  };
}
