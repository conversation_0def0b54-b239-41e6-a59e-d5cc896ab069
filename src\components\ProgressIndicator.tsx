'use client';

import { useTranslations } from 'next-intl';
import { Loader2 } from 'lucide-react';

interface ProgressIndicatorProps {
  progress: number;
  stage: 'uploading' | 'converting' | 'preparing' | 'finalizing';
}

export default function ProgressIndicator({ progress, stage }: ProgressIndicatorProps) {
  const t = useTranslations('progress');

  const getStageText = () => {
    switch (stage) {
      case 'uploading':
        return t('uploading', { progress: Math.round(progress) });
      case 'converting':
        return t('converting', { progress: Math.round(progress) });
      case 'preparing':
        return t('preparing');
      case 'finalizing':
        return t('finalizing');
      default:
        return '';
    }
  };

  return (
    <div className="w-full max-w-md mx-auto p-6 bg-white rounded-lg border-2 border-blue-200 shadow-lg">
      <div className="flex items-center justify-center mb-4">
        <div className="relative">
          <Loader2 className="animate-spin text-blue-600" size={32} />
          <div className="absolute inset-0 animate-pulse-slow">
            <div className="w-8 h-8 bg-blue-200 rounded-full opacity-30"></div>
          </div>
        </div>
      </div>
      
      <div className="text-center mb-4">
        <p className="text-lg font-medium text-gray-800 mb-2">
          {getStageText()}
        </p>
      </div>
      
      <div className="w-full bg-gray-200 rounded-full h-3 mb-2">
        <div 
          className="bg-blue-600 h-3 rounded-full transition-all duration-300 ease-out"
          style={{ width: `${Math.min(progress, 100)}%` }}
        />
      </div>
      
      <div className="text-center">
        <span className="text-sm text-gray-600">
          {Math.round(progress)}%
        </span>
      </div>
    </div>
  );
}
