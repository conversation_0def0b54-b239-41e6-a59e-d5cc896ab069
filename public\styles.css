/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.header {
    padding: 2rem 0;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.title-section h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: white;
    margin-bottom: 0.5rem;
}

.title-section p {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.9);
}

.language-toggle {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50px;
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.language-toggle:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

/* Main Content */
.main-content {
    padding-bottom: 3rem;
}

/* Instructions */
.instructions {
    margin-bottom: 3rem;
}

.steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    max-width: 900px;
    margin: 0 auto;
}

.step {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.step:hover {
    transform: translateY(-5px);
}

.step-number {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: bold;
    margin: 0 auto 1rem;
}

.step h3 {
    font-size: 1.1rem;
    color: #333;
    font-weight: 600;
}

/* Conversion Interface */
.conversion-interface {
    display: flex;
    justify-content: center;
}

.upload-section,
.preview-section,
.progress-section,
.download-section {
    width: 100%;
    max-width: 500px;
}

/* Upload Area */
.upload-area {
    background: white;
    border: 3px dashed #ddd;
    border-radius: 15px;
    padding: 3rem 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.upload-area:hover,
.upload-area.drag-over {
    border-color: #667eea;
    background: #f8f9ff;
    transform: translateY(-2px);
}

.upload-icon {
    font-size: 4rem;
    color: #ddd;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.upload-area:hover .upload-icon,
.upload-area.drag-over .upload-icon {
    color: #667eea;
    animation: bounce 1s infinite;
}

.upload-text {
    font-size: 1.3rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
}

.upload-subtext {
    color: #666;
    margin-bottom: 1.5rem;
}

.click-text {
    color: #667eea;
    font-weight: 600;
}

.upload-info {
    font-size: 0.9rem;
    color: #999;
}

.upload-info p {
    margin: 0.25rem 0;
}

/* Preview Section */
.preview-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.preview-video {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 10px;
    background: #f5f5f5;
    margin-bottom: 1rem;
}

.file-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.file-details {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.file-details i {
    font-size: 1.5rem;
    color: #667eea;
}

.file-name {
    font-weight: 600;
    color: #333;
    margin-bottom: 0.25rem;
}

.file-size {
    font-size: 0.9rem;
    color: #666;
}

.clear-button {
    background: #f5f5f5;
    border: none;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.clear-button:hover {
    background: #ff6b6b;
    color: white;
}

/* Progress Section */
.progress-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.progress-icon {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 1rem;
}

.progress-text {
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 1.5rem;
}

.progress-bar {
    width: 100%;
    height: 10px;
    background: #f0f0f0;
    border-radius: 5px;
    overflow: hidden;
    margin-bottom: 1rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 5px;
    transition: width 0.3s ease;
    width: 0%;
}

.progress-percentage {
    font-size: 1.1rem;
    font-weight: 600;
    color: #667eea;
}

/* Download Section */
.download-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.success-icon {
    font-size: 4rem;
    color: #4caf50;
    margin-bottom: 1rem;
}

.download-card h3 {
    font-size: 1.5rem;
    color: #333;
    margin-bottom: 0.5rem;
}

.download-card p {
    color: #666;
    margin-bottom: 2rem;
}

.download-buttons {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.download-button,
.convert-another-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    border: none;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.download-button {
    background: #4caf50;
    color: white;
}

.download-button:hover {
    background: #45a049;
    transform: translateY(-2px);
}

.convert-another-button {
    background: #f5f5f5;
    color: #333;
}

.convert-another-button:hover {
    background: #e0e0e0;
}

/* Error Message */
.error-message {
    background: #ffebee;
    border: 1px solid #ffcdd2;
    color: #c62828;
    padding: 1rem;
    border-radius: 10px;
    margin-top: 1rem;
    text-align: left;
    white-space: pre-line;
    line-height: 1.5;
}

/* Utility Classes */
.hidden {
    display: none !important;
}

/* Animations */
@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .title-section h1 {
        font-size: 2rem;
    }
    
    .header-content {
        text-align: center;
    }
    
    .steps {
        grid-template-columns: 1fr;
    }
    
    .upload-area {
        padding: 2rem 1rem;
    }
    
    .download-buttons {
        flex-direction: column;
    }
    
    .language-toggle span {
        display: none;
    }
}
