/**
 * @license lucide-react v0.523.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M12 20v-6", key: "1rm09r" }],
  ["path", { d: "M19.656 14H22", key: "170xzr" }],
  ["path", { d: "M2 14h12", key: "d8icqz" }],
  ["path", { d: "m2 2 20 20", key: "1ooewy" }],
  ["path", { d: "M20 20H4a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2", key: "s23sx2" }],
  ["path", { d: "M9.656 4H20a2 2 0 0 1 2 2v10.344", key: "ovjcvl" }]
];
const TouchpadOff = createLucideIcon("touchpad-off", __iconNode);

export { __iconNode, TouchpadOff as default };
//# sourceMappingURL=touchpad-off.js.map
