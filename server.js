const express = require('express');
const multer = require('multer');
const ffmpeg = require('fluent-ffmpeg');
const cors = require('cors');
const path = require('path');
const fs = require('fs-extra');
const { exec } = require('child_process');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// Create uploads and output directories
const uploadsDir = path.join(__dirname, 'uploads');
const outputDir = path.join(__dirname, 'output');

fs.ensureDirSync(uploadsDir);
fs.ensureDirSync(outputDir);

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, uploadsDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, 'video-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 100 * 1024 * 1024 // 100MB limit
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype.startsWith('video/mp4') || file.originalname.toLowerCase().endsWith('.mp4')) {
      cb(null, true);
    } else {
      cb(new Error('Only MP4 files are allowed'), false);
    }
  }
});

// Store conversion progress
const conversionProgress = new Map();

// Check if FFmpeg is available
function checkFFmpegAvailability() {
  return new Promise((resolve) => {
    exec('ffmpeg -version', (error) => {
      resolve(!error);
    });
  });
}

// Routes
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Upload and convert video
app.post('/api/convert', upload.single('video'), async (req, res) => {
  if (!req.file) {
    return res.status(400).json({ error: 'No video file uploaded' });
  }

  // Check if FFmpeg is available
  const ffmpegAvailable = await checkFFmpegAvailability();
  if (!ffmpegAvailable) {
    // Clean up uploaded file
    fs.remove(req.file.path).catch(console.error);
    return res.status(500).json({
      error: 'FFmpeg is not installed on this system. Please install FFmpeg to use video conversion.',
      installInstructions: 'Visit https://ffmpeg.org/download.html to download and install FFmpeg.'
    });
  }

  const inputPath = req.file.path;
  const outputFilename = `converted-${Date.now()}.webm`;
  const outputPath = path.join(outputDir, outputFilename);
  const conversionId = Date.now().toString();

  // Initialize progress tracking
  conversionProgress.set(conversionId, { progress: 0, stage: 'preparing' });

  res.json({ 
    message: 'Conversion started', 
    conversionId: conversionId,
    originalName: req.file.originalname
  });

  // Start FFmpeg conversion
  ffmpeg(inputPath)
    .videoFilters([
      // Scale to fit within 512x512 while preserving aspect ratio
      'scale=512:512:force_original_aspect_ratio=decrease',
      // Pad to exactly 512x512 with black bars
      'pad=512:512:(ow-iw)/2:(oh-ih)/2:black'
    ])
    .videoCodec('libvpx-vp9')
    .audioCodec('libopus')
    .videoBitrate('1000k')
    .audioBitrate('128k')
    .format('webm')
    .on('start', (commandLine) => {
      console.log('FFmpeg started with command:', commandLine);
      conversionProgress.set(conversionId, { progress: 5, stage: 'converting' });
    })
    .on('progress', (progress) => {
      const percent = Math.round(progress.percent || 0);
      conversionProgress.set(conversionId, { 
        progress: Math.min(percent, 95), 
        stage: 'converting' 
      });
      console.log(`Conversion progress: ${percent}%`);
    })
    .on('end', () => {
      console.log('Conversion finished');
      conversionProgress.set(conversionId, { progress: 100, stage: 'completed' });
      
      // Clean up input file
      fs.remove(inputPath).catch(console.error);
    })
    .on('error', (err) => {
      console.error('FFmpeg error:', err);
      conversionProgress.set(conversionId, { 
        progress: 0, 
        stage: 'error', 
        error: err.message 
      });
      
      // Clean up files
      fs.remove(inputPath).catch(console.error);
      fs.remove(outputPath).catch(console.error);
    })
    .save(outputPath);
});

// Get conversion progress
app.get('/api/progress/:conversionId', (req, res) => {
  const { conversionId } = req.params;
  const progress = conversionProgress.get(conversionId);
  
  if (!progress) {
    return res.status(404).json({ error: 'Conversion not found' });
  }
  
  res.json(progress);
});

// Download converted video
app.get('/api/download/:conversionId', (req, res) => {
  const { conversionId } = req.params;
  const progress = conversionProgress.get(conversionId);
  
  if (!progress || progress.stage !== 'completed') {
    return res.status(404).json({ error: 'Conversion not completed or not found' });
  }
  
  // Find the output file by looking for files created around the same time
  const outputFiles = fs.readdirSync(outputDir);
  const targetTime = parseInt(conversionId);
  let outputFile = null;

  for (const file of outputFiles) {
    const filePath = path.join(outputDir, file);
    const stats = fs.statSync(filePath);
    const fileTime = stats.birthtimeMs || stats.ctimeMs;

    // Check if file was created within 5 minutes of conversion start
    if (Math.abs(fileTime - targetTime) < 5 * 60 * 1000) {
      outputFile = file;
      break;
    }
  }
  
  if (!outputFile) {
    return res.status(404).json({ error: 'Converted file not found' });
  }
  
  const filePath = path.join(outputDir, outputFile);
  
  res.download(filePath, 'converted-video.webm', (err) => {
    if (err) {
      console.error('Download error:', err);
    } else {
      // Clean up the file after download
      setTimeout(() => {
        fs.remove(filePath).catch(console.error);
        conversionProgress.delete(conversionId);
      }, 5000); // Wait 5 seconds before cleanup
    }
  });
});

// Clean up old files periodically
setInterval(() => {
  const now = Date.now();
  const maxAge = 30 * 60 * 1000; // 30 minutes
  
  // Clean up old conversions from memory
  for (const [id, data] of conversionProgress.entries()) {
    if (now - parseInt(id) > maxAge) {
      conversionProgress.delete(id);
    }
  }
  
  // Clean up old files
  [uploadsDir, outputDir].forEach(dir => {
    fs.readdir(dir).then(files => {
      files.forEach(file => {
        const filePath = path.join(dir, file);
        fs.stat(filePath).then(stats => {
          if (now - stats.mtime.getTime() > maxAge) {
            fs.remove(filePath).catch(console.error);
          }
        }).catch(console.error);
      });
    }).catch(console.error);
  });
}, 10 * 60 * 1000); // Run every 10 minutes

app.listen(PORT, () => {
  console.log(`Server running on http://localhost:${PORT}`);
});
