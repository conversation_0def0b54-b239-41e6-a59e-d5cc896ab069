{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_2c4b2d10._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_1f9ae7ce.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(\\/?index|\\/?index\\\\.json))?[\\/#\\?]?$", "originalSource": "/"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(fr|en))(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/(fr|en)/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "Kr2BLuno4nooU6JpxzfWGakPdrFr/q7ciXsHIrRpb88=", "__NEXT_PREVIEW_MODE_ID": "c0d657d7b5de3a43c16a2d8fa8751483", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "a277da674bc6a0ec48d595d70f49a45d18d7dd0d1ea2291ea35751a706a20c9a", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "3d8c3c2b1154e814519c2fe621a99f71f124b6ab860c44d6d5981c0be5733c48"}}}, "sortedMiddleware": ["/"], "functions": {}}