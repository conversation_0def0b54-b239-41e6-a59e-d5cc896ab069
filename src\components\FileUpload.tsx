'use client';

import { useCallback, useState } from 'react';
import { useTranslations } from 'next-intl';
import { Upload, File, X } from 'lucide-react';

interface FileUploadProps {
  onFileSelect: (file: File) => void;
  isProcessing: boolean;
  selectedFile: File | null;
  onClearFile: () => void;
}

export default function FileUpload({ 
  onFileSelect, 
  isProcessing, 
  selectedFile, 
  onClearFile 
}: FileUploadProps) {
  const t = useTranslations('upload');
  const tErrors = useTranslations('errors');
  const [isDragOver, setIsDragOver] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const validateFile = (file: File): string | null => {
    // Check file type
    if (!file.type.includes('mp4')) {
      return tErrors('unsupportedFormat');
    }
    
    // Check file size (100MB limit)
    const maxSize = 100 * 1024 * 1024; // 100MB in bytes
    if (file.size > maxSize) {
      return tErrors('fileTooLarge');
    }
    
    return null;
  };

  const handleFileSelect = useCallback((file: File) => {
    const validationError = validateFile(file);
    if (validationError) {
      setError(validationError);
      return;
    }
    
    setError(null);
    onFileSelect(file);
  }, [onFileSelect, tErrors]);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  }, [handleFileSelect]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  if (selectedFile) {
    const videoUrl = URL.createObjectURL(selectedFile);

    return (
      <div className="w-full max-w-md mx-auto p-6 bg-white rounded-lg border-2 border-green-200 shadow-lg">
        {/* Video Preview */}
        <div className="mb-4">
          <video
            src={videoUrl}
            className="w-full h-48 object-cover rounded-lg bg-gray-100"
            controls
            muted
            onLoadedData={() => URL.revokeObjectURL(videoUrl)}
          />
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <File className="text-green-600" size={24} />
            <div>
              <p className="font-medium text-gray-900 truncate max-w-48">
                {selectedFile.name}
              </p>
              <p className="text-sm text-gray-500">
                {(selectedFile.size / (1024 * 1024)).toFixed(2)} MB
              </p>
            </div>
          </div>
          {!isProcessing && (
            <button
              onClick={onClearFile}
              className="p-1 hover:bg-gray-100 rounded-full transition-colors"
              aria-label="Remove file"
            >
              <X size={20} className="text-gray-500" />
            </button>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-md mx-auto">
      <div
        className={`
          relative border-2 border-dashed rounded-lg p-8 text-center transition-all duration-200
          ${isDragOver 
            ? 'border-blue-400 bg-blue-50' 
            : 'border-gray-300 hover:border-gray-400'
          }
          ${isProcessing ? 'opacity-50 pointer-events-none' : 'cursor-pointer'}
        `}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onClick={() => document.getElementById('file-input')?.click()}
      >
        <input
          id="file-input"
          type="file"
          accept=".mp4,video/mp4"
          onChange={handleFileInputChange}
          className="hidden"
          disabled={isProcessing}
        />
        
        <Upload
          size={48}
          className={`mx-auto mb-4 transition-all duration-300 ${
            isDragOver
              ? 'text-blue-500 animate-bounce-gentle'
              : 'text-gray-400 hover:text-gray-500'
          }`}
        />
        
        <p className="text-lg font-medium text-gray-700 mb-2">
          {t('dragDrop')}
        </p>
        
        <p className="text-gray-500 mb-4">
          {t('or')} <span className="text-blue-600 font-medium">{t('clickToSelect')}</span>
        </p>
        
        <div className="text-sm text-gray-400 space-y-1">
          <p>{t('maxSize')}</p>
          <p>{t('supportedFormats')}</p>
        </div>
      </div>
      
      {error && (
        <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-700 text-sm">{error}</p>
        </div>
      )}
    </div>
  );
}
